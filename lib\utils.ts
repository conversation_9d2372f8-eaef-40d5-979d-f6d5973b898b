import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

export function formatDate(date: Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

export function calculateRentalPrice(
  startDate: Date,
  endDate: Date,
  pricePerDay: number
): { totalDays: number; totalPrice: number; breakdown: string } {
  const timeDiff = endDate.getTime() - startDate.getTime()
  const hoursDiff = timeDiff / (1000 * 3600)
  
  let totalDays = Math.ceil(hoursDiff / 24)
  
  // Minimum 3 days billing
  if (totalDays < 3) {
    totalDays = 3
  }
  
  // 2-hour tolerance for same day return
  if (hoursDiff <= 2) {
    totalDays = 1
  }
  
  const totalPrice = totalDays * pricePerDay
  const breakdown = `${totalDays} day${totalDays > 1 ? 's' : ''} × ${formatCurrency(pricePerDay)}`
  
  return { totalDays, totalPrice, breakdown }
}