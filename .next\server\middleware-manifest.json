{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GR3ucCkhMoA2xFidxDKkdIRtbol5/QOQaP0f6MSOcaM=", "__NEXT_PREVIEW_MODE_ID": "599dd79b6f6ac909ba2923901e092319", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6e3d6b4541ebb4d6e3731cbc0e9f0fcb14ccc6f56834b970dd8e14994c01e662", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c680e81f336efd901c0218b6416e26d8e92ce843d1655e93c4add88f4c8eeef0"}}}, "functions": {}, "sortedMiddleware": ["/"]}