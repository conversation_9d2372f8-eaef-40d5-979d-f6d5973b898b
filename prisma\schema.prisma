generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String        @id @default(cuid())
  name          String?
  email         String        @unique
  emailVerified DateTime?
  image         String?
  firstName     String?
  lastName      String?
  phone         String?
  city          String?
  country       String?
  birthday      DateTime?
  isOnboarded   Boolean       @default(false)
  role          UserRole      @default(USER)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  accounts      Account[]
  sessions      Session[]
  reservations  Reservation[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Car {
  id           String        @id @default(cuid())
  make         String
  model        String
  year         Int
  pricePerDay  Float
  fuelType     String?
  seats        Int?
  transmission String?
  imageUrl     String?
  features     String?
  available    Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  reservations Reservation[]
}

model Reservation {
  id           String            @id @default(cuid())
  userId       String
  carId        String
  startDate    DateTime
  endDate      DateTime
  startPlace   String?
  endPlace     String?
  flightNumber String?
  totalPrice   Float?
  status       ReservationStatus @default(PENDING)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  user         User              @relation(fields: [userId], references: [id])
  car          Car               @relation(fields: [carId], references: [id])
}

enum UserRole {
  USER
  ADMIN
}

enum ReservationStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}